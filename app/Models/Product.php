<?php

namespace App\Models;

use App\Models\Concerns\ManagesInventory;
use App\Models\Concerns\ManagesPricing;
use App\Presenters\ProductPresenter;
use App\Support\Enums\ProductType;
use App\ThemeBuilder\ClassicThemeBuilder;
use App\Traits\SeoTrait;
use App\Traits\SettingsTrait;
use Cviebrock\EloquentSluggable\Services\SlugService;
use Cviebrock\EloquentSluggable\Sluggable;
use EloquentFilter\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;
use Laravel\Scout\Searchable;
use Meilisearch\Endpoints\Indexes;

/**
 * App\Models\Product
 *
 * @property int $id
 * @property string $title
 * @property string $slug
 * @property int $type_id
 * @property int|null $fulfillment_id
 * @property string|null $sku
 * @property string|null $barcode
 * @property int|null $vendor_id
 * @property int $unit_price
 * @property int $wholesale_unit_price
 * @property int $sale_unit_price
 * @property string $unit_of_issue
 * @property string $unit_description
 * @property float $weight
 * @property int $item_cost
 * @property int $inventory_type
 * @property int $inventory
 * @property int $stock_out_inventory
 * @property int|null $oos_threshold_inventory
 * @property int $processor_inventory
 * @property int $other_inventory
 * @property int $shared_inventory
 * @property int $shared_inventory_id
 * @property string $track_inventory
 * @property int $back_order
 * @property int $back_order_limit
 * @property int $class_id
 * @property int $subclass_id
 * @property string $description
 * @property string $seo_description
 * @property string|null $cover_photo
 * @property string|null $cover_photo_thumbnail
 * @property string $ingredients
 * @property string $keywords
 * @property string $notes
 * @property string|null $fulfillment_instructions
 * @property int $sale
 * @property int $visible
 * @property int $hide_from_search
 * @property bool $is_bundle
 * @property int $is_grouped
 * @property int $wholesale
 * @property int $taxable
 * @property string|null $order_start
 * @property string|null $order_end
 * @property string|null $sale_start
 * @property string|null $sale_end
 * @property string $accounting_class
 * @property int $seo_visibility
 * @property string|null $canonical_url
 * @property string|null $head_tags
 * @property string|null $body_tags
 * @property string|null $page_title
 * @property string|null $page_description
 * @property mixed $settings
 * @property string $custom_sort
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int|null $category_id
 * @property int|null $category_position
 * @property-read \Illuminate\Database\Eloquent\Collection|Event[] $events
 * @property-read \Illuminate\Database\Eloquent\Collection|Product[] $bundle
 * @property-read int|null $bundle_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Product[] $containingBundles
 * @property-read int|null $containing_bundles_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Collection[] $collections
 * @property-read int|null $collections_count
 * @property-read \App\Models\Pickup|null $fulfillmentLocation
 * @property mixed $custom_fields
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\GiftCertificate[] $giftCards
 * @property-read int|null $gift_cards_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\InventoryAdjustment[] $inventoryAdjustments
 * @property-read int|null $inventory_adjustments_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\OrderItem[] $onOrder
 * @property-read int|null $on_order_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Pickup[] $pickups
 * @property-read int|null $pickups_count
 * @property-read \App\Models\ProductPrice|null $price
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Protocol[] $protocols
 * @property-read int|null $protocols_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Product[] $related
 * @property-read int|null $related_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Review[] $reviews
 * @property-read int|null $reviews_count
 * @property-write mixed $price_raw
 * @property-read \App\Models\Schedule|null $schedule
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Tag[] $tags
 * @property-read int|null $tags_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Product[] $variants
 * @property-read int|null $variants_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Media[] $photos
 * @property-read int|null $photos_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Price[] $prices
 * @property-read int|null $prices_count
 * @property-read \App\Models\Price|null $defaultPrice
 * @property-read \App\Models\Vendor|null $vendor
 * @method static Builder|Product excluding(\Illuminate\Support\Collection $excluded)
 * @method static Builder|Product findSimilarSlugs(string $attribute, array $config, string $slug)
 * @method static Builder|Product giftCard()
 * @method static Builder|Product lowInventory()
 * @method static Builder|Product newModelQuery()
 * @method static Builder|Product newQuery()
 * @method static \Illuminate\Database\Query\Builder|Product onlyTrashed()
 * @method static Builder|Product query()
 * @method static Builder|Product whereAccountingClass($value)
 * @method static Builder|Product whereBackOrder($value)
 * @method static Builder|Product whereBackOrderLimit($value)
 * @method static Builder|Product whereBarcode($value)
 * @method static Builder|Product whereBodyTags($value)
 * @method static Builder|Product whereCanonicalUrl($value)
 * @method static Builder|Product whereClassId($value)
 * @method static Builder|Product whereCoverPhoto($value)
 * @method static Builder|Product whereCoverPhotoThumbnail($value)
 * @method static Builder|Product whereCreatedAt($value)
 * @method static Builder|Product whereCustomSort($value)
 * @method static Builder|Product whereDeletedAt($value)
 * @method static Builder|Product whereDescription($value)
 * @method static Builder|Product whereFulfillmentId($value)
 * @method static Builder|Product whereFulfillmentInstructions($value)
 * @method static Builder|Product whereHeadTags($value)
 * @method static Builder|Product whereHideFromSearch($value)
 * @method static Builder|Product whereId($value)
 * @method static Builder|Product whereIngredients($value)
 * @method static Builder|Product whereInventory($value)
 * @method static Builder|Product whereInventoryType($value)
 * @method static Builder|Product whereIsBundle($value)
 * @method static Builder|Product whereIsGrouped($value)
 * @method static Builder|Product whereItemCost($value)
 * @method static Builder|Product whereKeywords($value)
 * @method static Builder|Product whereNotes($value)
 * @method static Builder|Product whereOosThresholdInventory($value)
 * @method static Builder|Product whereOrderEnd($value)
 * @method static Builder|Product whereOrderStart($value)
 * @method static Builder|Product whereOtherInventory($value)
 * @method static Builder|Product wherePageDescription($value)
 * @method static Builder|Product wherePageTitle($value)
 * @method static Builder|Product whereProcessorInventory($value)
 * @method static Builder|Product whereSale($value)
 * @method static Builder|Product whereSaleEnd($value)
 * @method static Builder|Product whereSaleStart($value)
 * @method static Builder|Product whereSaleUnitPrice($value)
 * @method static Builder|Product whereSeoDescription($value)
 * @method static Builder|Product whereSeoVisibility($value)
 * @method static Builder|Product whereSettings($value)
 * @method static Builder|Product whereSharedInventory($value)
 * @method static Builder|Product whereSharedInventoryId($value)
 * @method static Builder|Product whereSku($value)
 * @method static Builder|Product whereSlug($value)
 * @method static Builder|Product whereStockOutInventory($value)
 * @method static Builder|Product whereSubclassId($value)
 * @method static Builder|Product whereTaxable($value)
 * @method static Builder|Product whereTitle($value)
 * @method static Builder|Product whereTrackInventory($value)
 * @method static Builder|Product whereTypeId($value)
 * @method static Builder|Product whereUnitDescription($value)
 * @method static Builder|Product whereUnitOfIssue($value)
 * @method static Builder|Product whereUnitPrice($value)
 * @method static Builder|Product whereUpdatedAt($value)
 * @method static Builder|Product whereVendorId($value)
 * @method static Builder|Product whereVisible($value)
 * @method static Builder|Product whereWeight($value)
 * @method static Builder|Product whereWholesale($value)
 * @method static Builder|Product whereWholesaleUnitPrice($value)
 * @method static \Illuminate\Database\Query\Builder|Product withTrashed()
 * @method static Builder|Product withUniqueSlugConstraints(\Illuminate\Database\Eloquent\Model $model, string $attribute, array $config, string $slug)
 * @method static \Illuminate\Database\Query\Builder|Product withoutTrashed()
 * @property-read mixed $available_inventory
 * @property-read mixed $group_price
 * @property-read bool $has_virtual_fulfilment
 * @property-read string|null $most_recent_event
 * @method static \Database\Factories\ProductFactory factory($count = null, $state = [])
 * @method static Builder|Product filter(array $input = [], $filter = null)
 * @method static Builder|Product paginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static Builder|Product simplePaginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static Builder|Product whereBeginsWith($column, $value, $boolean = 'and')
 * @method static Builder|Product whereEndsWith($column, $value, $boolean = 'and')
 * @method static Builder|Product whereLike($column, $value, $boolean = 'and')
 * @property int|null $schedule_id
 * @property string|null $summary
 * @method static Builder|Product whereScheduleId($value)
 * @method static Builder|Product whereSummary($value)
 * @mixin \Eloquent
 */
class Product extends Model
{
    use HasFactory;

    use PresentableTrait, Sluggable, SoftDeletes, SettingsTrait, Filterable, SeoTrait, ManagesInventory, ManagesPricing;

    use Searchable;

    public const IMPORT_TYPES = [
        'create' => 'Import new products',
        'update_by_id' => 'Update existing products by product ID',
        'update_by_sku' => 'Update existing products by product SKU',
        'update_price_tiers' => 'Update existing products price tiers',
    ];

    protected $guarded = [];

    protected $appends = [
        'has_virtual_fulfilment'
    ];

    protected $presenter = ProductPresenter::class;

    public static function attributesForSearch(): array
    {
        return [
            'products.id',
            'title',
            'vendor_id',
            'unit_price',
            'sale_unit_price',
            'wholesale_unit_price',
            'products.slug',
            'unit_of_issue',
            'unit_description',
            'weight',
            'inventory',
            'other_inventory',
            'oos_threshold_inventory',
            'track_inventory',
            'back_order',
            'cover_photo_thumbnail',
            'cover_photo',
            'sale',
            'sku',
            'is_bundle',
            'settings',
            'fulfillment_id',
            'canonical_url',
            'type_id',
            'schedule_id',
        ];
    }

    public static function attributesForCart(): array
    {
        return [
            'id',
            'title',
            'slug',
            'type_id',
            'fulfillment_id',
            'schedule_id',
            'category_id',
            'sku',
            'barcode',
            'vendor_id',
            'unit_price',
            'wholesale_unit_price',
            'sale_unit_price',
            'unit_of_issue',
            'unit_description',
            'weight',
            'item_cost',
            'inventory_type',
            'inventory',
            'stock_out_inventory',
            'oos_threshold_inventory',
            'processor_inventory',
            'other_inventory',
            'shared_inventory',
            'shared_inventory_id',
            'track_inventory',
            'back_order',
            'back_order_limit',
            'class_id',
            'subclass_id',
            'cover_photo',
            'cover_photo_thumbnail',
            'sale',
            'visible',
            'hide_from_search',
            'is_bundle',
            'is_grouped',
            'wholesale',
            'taxable',
            'order_start',
            'order_end',
            'sale_start',
            'sale_end',
            'accounting_class',
            'seo_visibility',
            'canonical_url',
            'settings',
            'custom_sort',
            'created_at',
            'updated_at',
            'deleted_at'
        ];
    }

    public static function advancedSearch(string $term)
    {
        if (config('scout.driver') !== 'meilisearch') {
            return Product::search($term)
                ->where('hide_from_search', false);
        }

        return Product::search(
            $term,
            function(Indexes $meiliSearch, string $query, array $options) {
                $options['attributesToSearchOn'] = ['title', 'keywords'];
                $options['filter'] = ["search_visibility = 'show'"];
                return $meiliSearch->search($query, $options);
            }
        );
    }

    protected static function booted()
    {
        static::saved(function ($product) {
            if (!$product->isDirty(['unit_price', 'sale_unit_price'])) {
                return;
            }

            Price::upsert(
                [
                    'product_id' => $product->id,
                    'quantity' => 1,
                    'unit_price' => $product->unit_price,
                    'sale_unit_price' => $product->sale_unit_price,
                ],
                uniqueBy: ['product_id', 'quantity'],
                update: ['unit_price', 'sale_unit_price']
            );
        });


    }

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title'
            ]
        ];
    }

    /**
     * @return BelongsToMany<Product, $this>
     */
    public function bundle(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'bundle_product', 'bundle_id', 'product_id')
            ->withPivot('qty');
    }

    /**
     * @return BelongsToMany<Product, $this>
     */
    public function containingBundles(): BelongsToMany
    {
        return $this->belongsToMany(
            Product::class,
            'bundle_product',
            'product_id',
            'bundle_id'
        )
            ->withPivot('qty');
    }

    public function duplicate(string $title = null): Product
    {
        $copy = $this->replicate();

        $copy->title = $title ?: $copy->title . ' (COPY)';
        $copy->sku = null;
        $copy->fulfillment_instructions = $copy->title;

        (new SlugService)->slug($copy, true);

        $copy->save();

        $this->load('collections', 'protocols', 'tags');

        // Copy over collections to new product.
        foreach ($this->collections as $collection) {
            $collection->products()->attach([$copy->id]);
        }

        // Copy over protocols to new product.
        $copy->protocols()->sync($this->protocols->pluck('id')->toArray());

        // Copy over tags to new product.
        $copy->tags()->attach($this->tags->pluck('id')->toArray());

        return $copy;
    }

    /**
     * @return BelongsToMany<Protocol, $this>
     */
    public function protocols(): BelongsToMany
    {
        return $this->belongsToMany(Protocol::class);
    }

    /**
     * @return BelongsToMany<Tag, $this>
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)->withTimestamps();
    }

    /**
     * @return BelongsTo<PackingGroup, $this>
     */
    public function packingGroup(): BelongsTo
    {
        return $this->belongsTo(PackingGroup::class, 'inventory_type', 'id');
    }

    /**
     * @return HasMany<Price, $this>
     */
    public function prices(): HasMany
    {
        return $this->hasMany(Price::class);
    }

    /**
     * @return HasOne<Price, $this>
     */
    public function defaultPrice(): HasOne
    {
        return $this->hasOne(Price::class)->where('quantity', 1);
    }

    public function getWeight()
    {
        return is_null($this->price) ? $this->weight : $this->price->weight;
    }

    public function getSku(): string
    {
        return strlen($this->sku) ? $this->sku : $this->slug;
    }

    public function scopeLowInventory(Builder $query): Builder
    {
        return $query->select(['id', 'inventory', 'title'])
            ->where('inventory', '<', 10)
            ->where('track_inventory', 'yes')
            ->orderBy('inventory');
    }

    public function scopeGiftCard(Builder $query): Builder
    {
        return $query->where('type_id', ProductType::GIFT_CARD->value);
    }

    public function scopeExcluding(Builder $query, \Illuminate\Support\Collection $excluded): Builder
    {
        return $query->whereNotIn('products.id', $excluded);
    }

    public function scopeBasicSearch(Builder $query, string $search): Builder
    {
        return $query->where('products.title', 'LIKE', '%' . $search . '%')
            ->orWhere('products.sku',  $search)
            ->orWhere('products.keywords', 'LIKE', '%' . $search . '%')
            ->orderBy('title')
            ->orderBy('keywords');
    }

    /**
     * @return BelongsToMany<Collection, $this>
     */
    public function collections(): BelongsToMany
    {
        return $this->belongsToMany(Collection::class);
    }

    /**
     * @return BelongsTo<Vendor, $this>
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * @return BelongsTo<Category, $this>
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * @return BelongsToMany<Pickup, $this>
     */
    public function pickups(): BelongsToMany
    {
        return $this->belongsToMany(Pickup::class);
    }

    /**
     * @return HasOne<Pickup, $this>
     */
    public function fulfillmentLocation(): HasOne
    {
        return $this->hasOne(Pickup::class, 'id', 'fulfillment_id');
    }

    /**
     * @return HasMany<InventoryAdjustment, $this>
     */
    public function inventoryAdjustments(): HasMany
    {
        return $this->hasMany(InventoryAdjustment::class)
            ->orderBy('created_at', 'desc');
    }

    /**
     * @return HasMany<OrderItem, $this>
     */
    public function onOrder(): HasMany
    {
        return $this->hasMany(OrderItem::class)
            ->select(['id', 'product_id', 'title', 'qty']);
    }

    /**
     * @return BelongsToMany<Product, $this>
     */
    public function related(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'related_products', 'product_id', 'related_id');
    }

    /**
     * @return HasMany<Review, $this>
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class)->orderBy('created_at', 'desc');
    }

    /**
     * @return HasOne<ProductPrice, $this>
     */
    public function price(): HasOne
    {
        return $this->hasOne(ProductPrice::class)->with('group');
    }

    /**
     * @return HasMany<GiftCertificate, $this>
     */
    public function giftCards(): HasMany
    {
        return $this->hasMany(GiftCertificate::class);
    }

    /**
     * @return BelongsToMany<Product, $this>
     */
    public function variants(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_variants', 'product_id', 'variant_id')
            ->orderBy('sort');
    }

    /**
     * @return MorphOne<Event, $this>
     */
    public function mostRecentEvent(): MorphOne
    {
        return $this->events()->one()->latestOfMany('created_at');
    }

    /**
     * @return MorphMany<Event, $this>
     */
    public function events(): MorphMany
    {
        return $this->morphMany(Event::class, 'model');
    }

    /**
     * @param mixed $value
     */
    public function setSkuAttribute($value): void
    {
        $this->attributes['sku'] = $value == '' ? null : $value;
    }

    /**
     * @param mixed $value
     */
    public function setPriceRawAttribute($value): void
    {
        $this->attributes['unit_price'] = $value;
    }

    /**
     * @param mixed $value
     */
    public function setItemCostAttribute($value): void
    {
        $this->attributes['item_cost'] = formatCurrencyForDB($value);
    }

    /**
     * @param mixed $value
     */
    public function setWholesaleUnitPriceAttribute($value): void
    {
        $this->attributes['wholesale_unit_price'] = formatCurrencyForDB($value);
    }

    /**
     * @param mixed $value
     */
    public function setVendorIdAttribute($value): void
    {
        $this->attributes['vendor_id'] = is_numeric($value) ? $value : null;
    }

    public function getAvailableInventoryAttribute()
    {
        return $this->remainingInventory();
    }

    public function hasVariants(): bool
    {
        return $this->variants->isNotEmpty();
    }

    public function schedule(): BelongsTo
    {
        return $this->belongsTo(Schedule::class);
    }

    public function resolveChildRouteBinding($childType, $value, $field)
    {
        if ($childType === 'code') {
            $childType = 'giftCard';
        }
        return parent::resolveChildRouteBinding($childType, $value, $field);
    }

    public function cartActionLabel(): string
    {
        $custom_label = $this->setting('cart_action_label');

        if ($this->isPreOrder()) {
            return $custom_label ?? 'Pre-order';
        }

        if ($this->isGiftCard() && $this->isFulfilledVirtually()) {
            return $custom_label ?? 'Buy Now';
        }

        if (empty($custom_label)) {
            return app(ClassicThemeBuilder::class)->addToCartLabel();
        }

        return $custom_label;

    }

    public function isPreOrder(): bool
    {
        return $this->fulfillment_id > 0 || ! is_null($this->schedule_id) || $this->type_id === ProductType::PREORDER->value;
    }

    public function isGiftCard(): bool
    {
        return $this->type_id === ProductType::GIFT_CARD->value;
    }

    public function isFulfilledVirtually(): bool
    {
        return $this->fulfilmentMethod() === 'virtual';
    }

    public function fulfilmentMethod(): string
    {
        return $this->setting('fulfilment_method', 'virtual');
    }

    public function orderActionLabel(bool $is_subscription_order = false): string
    {
        return $is_subscription_order
            ? $this->subscriptionOrderActionLabel()
            : $this->oneTimeOrderActionLabel();
    }

    private function subscriptionOrderActionLabel(): string
    {
        $custom_label = $this->setting('cart_action_label');

        if ($this->isPreOrder()) {
            return $custom_label ?? 'Pre-order';
        }

        if ($this->isGiftCard() && $this->isFulfilledVirtually()) {
            return $custom_label ?? 'Buy Now';
        }

        return app(ClassicThemeBuilder::class)->addToSubscriptionLabel();

    }

    private function oneTimeOrderActionLabel(): string
    {
        $custom_label = $this->setting('cart_action_label');

        if ($this->isPreOrder()) {
            return $custom_label ?? 'Pre-order';
        }

        if ($this->isGiftCard() && $this->isFulfilledVirtually()) {
            return $custom_label ?? 'Buy Now';
        }

        if (empty($custom_label)) {
            return app(ClassicThemeBuilder::class)->addToOrderLabel();
        }

        return $custom_label;
    }

    public function canBePurchasedIndividually(): bool
    {
        return $this->isPreOrder() || $this->isGiftCard();
    }

    public function checkoutPath(): ?string
    {
        if ( ! $this->isPreOrder() && ! $this->isGiftCard()) {
            return null;
        }

        return route('store.products.checkout.show', [$this->slug], false);
    }

    public function hasStandardCheckoutFlow(): bool
    {
        return $this->isGiftCard()
            || $this->checkoutFlow() === 'standard';
    }

    public function checkoutFlow(): string
    {
        return $this->setting('checkout_flow', 'beta');
    }

    public function hasEstimatedDeliveryDate(): bool
    {
        return $this->scheduleType() === 'estimate';
    }

    public function scheduleType(): string
    {
        return $this->setting('schedule_type', 'actual');
    }

    public function getHasVirtualFulfilmentAttribute(): bool
    {
        return $this->isFulfilledVirtually();
    }

    public function hasCalloutMessage(): bool
    {
        return !is_null($this->calloutMessage());
    }

    public function calloutMessage() : ?string
    {
        if ($this->isOnSale() && !empty($this->setting('sale_message'))) {
            return $this->setting('sale_message');
        }

        if (!empty($this->setting('standard_callout_message'))) {
            return $this->setting('standard_callout_message');
        }

        return null;
    }

    public function hasLimitPerCustomer(): bool
    {
        return !is_null($this->limitPerCustomer());
    }

    public function limitPerCustomer(): ?int
    {
        $limit = $this->setting('quantity_limit');
        return ! empty($limit) && (int) $limit > 0 ? (int) $limit : null;
    }

    public function toCartProduct(int $quantity = 1): \App\Cart\Product
    {
        return new \App\Cart\Product(
            id: $this->id,
            type_id: $this->type(),
            unit_of_issue: $this->unit_of_issue,
            title: $this->title,
            unit_price: $this->getUnitPrice($quantity),
            unit_weight: $this->weight,
            is_taxable: (bool) $this->taxable
        );
    }

    /**
     * This function is to help setting type_id retroactively on existing products. Once all products have the
     * correct type_id set on them, this function and its usages can be removed.
     */
    public function type(): int
    {
        return match(true) {
            $this->type_id === ProductType::PREORDER->value || $this->isPreOrder() => ProductType::PREORDER->value,
            $this->isGiftCard() => ProductType::GIFT_CARD->value,
            default => ProductType::STANDARD->value,
        };
    }

    public function hasOrderMinimum(): bool
    {
        return $this->orderMinimum() > 0;
    }

    public function orderMinimum(): int
    {
        return formatCurrencyForDB($this->setting('order_minimum') ?? 0);
    }

    public function isTaxable(): bool
    {
        return (bool) $this->taxable;
    }

    public function searchableAs(): string
    {
        return app()->environment() . '_products_index';
    }

    /**
     * Some search engines such as Meilisearch will only perform filter operations (>, <, etc.)
     * on data of the correct type. So, when using these search engines and customizing your
     * searchable data, you should ensure that numeric values are cast to their correct type.
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        $searchable_in_store = (boolval($this->getRawOriginal('visible')) && ! boolval($this->hide_from_search));

        if (config('scout.driver') === 'database') {
            return [
                'id' => $this->id,
                'title' => $this->title,
                'keywords' => $this->keywords,
                'search_visibility' => $searchable_in_store ? 'show' : 'hide',
            ];
        }

        return [
            'id' => (int) $this->id,
            'title' => $this->title,
            'keywords' => $this->keywords,
            'slug' => $this->slug,
            'sku' => $this->sku,
            'search_visibility' => $searchable_in_store ? 'show' : 'hide',
        ];
    }

    public function photos(): BelongsToMany
    {
        return $this->belongsToMany(Media::class)
            ->where('type', 'image')
            ->withPivot('sort')
            ->orderBy('sort');
    }

    public function mainPhoto():HasOneThrough
    {
        return $this->HasOneThrough(
            Media::class,
            MediaProduct::class,
            'product_id',
            'id',
            'id',
            'media_id')
            ->where('type', 'image')
            ->orderBy('sort');

    }

    protected function unitPrice(): Attribute
    {
        return Attribute::make(
            set: fn ($value) => formatCurrencyForDB($value)
        );
    }

    protected function saleUnitPrice(): Attribute
    {
        return Attribute::make(
            set: fn ($value) => formatCurrencyForDB($value)
        );
    }

    protected function casts(): array
    {
        return [
            'weight' => 'float',
            'is_bundle' => 'boolean'
        ];
    }

}
