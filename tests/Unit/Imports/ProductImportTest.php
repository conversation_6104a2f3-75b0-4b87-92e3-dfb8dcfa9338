<?php

namespace Tests\Unit\Imports;

use App\Events\InventoryAdjusted;
use App\Imports\ProductImport;
use App\Models\Collection;
use App\Models\PackingGroup;
use App\Models\Price;
use App\Models\Product;
use App\Models\Protocol;
use App\Models\Tag;
use App\Models\User;
use App\Models\Vendor;
use App\Support\Enums\ProductType;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductImportTest extends TenantTestCase
{
    #[Test]
    public function it_creates_new_product_when_default_csv_columns_are_passed(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 450,
            ])
        ]));

        $this->assertDatabaseHas(Product::class, [
            'title' => 'New Beef Steak',
            'unit_price' => 45000,
            'weight' => 0.000,
            'slug' => 'new-beef-steak',
            'unit_of_issue' => 'package',
            'keywords' => null,
            'visible' => 0,
            'hide_from_search' => 0,
            'deleted_at' => null,
            'inventory' => 0,
            'track_inventory' => 'yes',
            'taxable' => 0,
            'type_id' => 1,
            'is_bundle' => 0,
            'summary' => null,
            'description' => null,
            'unit_description' => null,
            'ingredients' => null,
            'sku' => null,
            'sale_unit_price' => 0,
            'item_cost' => 0,
            'processor_inventory' => null,
            'other_inventory' => 0,
            'oos_threshold_inventory' => null,
            'stock_out_inventory' => 0,
            'barcode' => null,
            'page_title' => null,
            'seo_description' => null,
            'custom_sort' => null,
            'accounting_class' => null,
            'inventory_type' => 1,
            'vendor_id' => null,
        ]);
    }

    #[Test]
    public function it_creates_new_product_when_all_csv_columns_are_passed(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 450,
                'weight_lb' => 25,
                'type' => 'preorder',
                'summary' => 'Those Who Doubt This Summary',
                'description' => 'Those Who Doubt This Description',
                'collection' => 'MaBeef',
                'tag' => 'Omnivores & Carnivores',
                'unit_description' => 'Those Who Doubt This Unit Description',
                'ingredients' => 'Those Who Doubt This Ingredients',
                'sku' => 'RK423',
                'pricing_unit' => 'weight',
                'sale_unit_price' => 20,
                'item_cost' => 15,
                'on_site_inventory_actual' => 10,
                'on_site_inventory_to_add' => 0,
                'on_site_inventory_to_remove' => 0,
                'off_site_inventory' => 2,
                'subscription_reserve' => 12,
                'reorder_threshold' => 15,
                'storage_location' => 'Those Who Doubt This Storage Location',
                'packing_group' =>  'dry',
                'barcode' => 'Those Who Doubt This BarCode',
                'vendor' => 'Blessed Vendor',
                'accounting_class_id' => 'Those Who Doubt This Accounting Class Id',
                'visibility_status' => 'hidden',
                'tax_status' => 'off',
                'meta_title' => 'Those Who Doubt This Meta Title',
                'meta_description' => 'Those Who Doubt This Meta Description',
            ])
        ]));

        $this->assertDatabaseHas(Product::class, [
            'title' => 'New Beef Steak',
            'slug' => 'new-beef-steak',
            'unit_price' => 45000,
            'type_id' => ProductType::PREORDER->value,
            'unit_of_issue' => 'weight',
            'weight' => 25,
            'visible' => 0,
            'inventory' => 10,
            'taxable' => 0,
            'summary' => 'Those Who Doubt This Summary',
            'description' => 'Those Who Doubt This Description',
            'unit_description' => 'Those Who Doubt This Unit Description',
            'ingredients' => 'Those Who Doubt This Ingredients',
            'sku' => 'RK423',
            'sale_unit_price' => formatCurrencyForDB(20),
            'item_cost' => formatCurrencyForDB(15),
            'other_inventory' => 2,
            'oos_threshold_inventory' => 12,
            'stock_out_inventory' => 15,
            'barcode' => 'Those Who Doubt This BarCode',
            'page_title' => 'Those Who Doubt This Meta Title',
            'seo_description' => 'Those Who Doubt This Meta Description',
            'custom_sort' => 'Those Who Doubt This Storage Location',
            'accounting_class' => 'Those Who Doubt This Accounting Class Id',
            'inventory_type' => 2,
        ]);

        $product = Product::where('title', 'New Beef Steak')->first();

        $this->assertDatabaseHas(Collection::class, [
            'title' => 'MaBeef',
            'slug' => 'mabeef',
        ]);

        $this->assertTrue($product->collections()->where('title', 'MaBeef')->exists());

        $this->assertDatabaseHas(Tag::class, [
            'title' => 'Omnivores & Carnivores',
        ]);

        $this->assertTrue($product->tags()->where('title', 'Omnivores & Carnivores')->exists());

        $this->assertDatabaseHas(Vendor::class, [
            'title' => 'Blessed Vendor',
        ]);

        $this->assertEquals('Blessed Vendor', $product->vendor->title);
    }

    #[Test]
    public function it_can_updates_by_id_when_csv_columns_dont_match_attributes(): void
    {
        $product = Product::factory()->create(['title' => 'Pork Steak',  'unit_price' => 450, 'weight' => 25,]);

        $import = new ProductImport('update_by_id');
        $import->collection(collect([
            collect([
                'id' => $product->id,
                'foo' => 'bar'
            ])
        ]));

        $product->refresh();

        $this->assertEquals(45000, $product->unit_price);
        $this->assertDatabaseHas(Product::class, [
            'title' => 'Pork Steak',
            'slug' => $product->slug,
            'unit_price' => $product->unit_price,
            'summary' => $product->summary,
            'unit_of_issue' => $product->unit_of_issue,
            'weight' => number_format($product->weight, 3),
            'keywords' => $product->keywords,
            'visible' => (int) $product->visible,
            'hide_from_search' => (int) $product->hide_from_search,
            'deleted_at' => $product->deleted_at,
            'inventory' => $product->inventory,
            'track_inventory' => $product->track_inventory,
            'taxable' => (int) $product->taxable,
            'type_id' => $product->type_id,
            'is_bundle' => (int) $product->is_bundle,
            'id' => $product->id,
        ]);
    }

    #[Test]
    public function it_can_updates_by_id_when_csv_columns_match_attributes(): void
    {
        $product = Product::factory()->create(['title' => 'Pork Steak',  'unit_price' => 450, 'weight' => 25,]);

        $import = new ProductImport('update_by_id');
        $import->collection(collect([
            collect([
                'id' => $product->id,
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
            ])
        ]));

        $product->refresh();

        $this->assertEquals(35000, $product->unit_price);
        $this->assertDatabaseHas(Product::class, [
            'title' => 'New Beef Steak',
            'slug' => $product->slug,
            'unit_price' => 35000,
            'unit_of_issue' => $product->unit_of_issue,
            'weight' => 15.000,
            'keywords' => $product->keywords,
            'visible' => (int) $product->visible,
            'hide_from_search' => (int) $product->hide_from_search,
            'deleted_at' => $product->deleted_at,
            'inventory' => $product->inventory,
            'track_inventory' => $product->track_inventory,
            'taxable' => (int) $product->taxable,
            'type_id' => $product->type_id,
            'is_bundle' => (int) $product->is_bundle,
            'id' => $product->id,
        ]);
    }

    #[Test]
    public function it_can_updates_by_sku_when_csv_columns_dont_match_attributes(): void
    {
        $product = Product::factory()->create(['sku' => 'RK456', 'unit_price' => 350]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product->sku,
                'bar' => 'zoo',
            ])
        ]));

        $product->refresh();

        $this->assertEquals(35000, $product->unit_price);
        $this->assertDatabaseHas(Product::class, [
            'title' => $product->title,
            'slug' => $product->slug,
            'unit_price' => $product->unit_price,
            'unit_of_issue' => $product->unit_of_issue,
            'weight' => number_format($product->weight, 3),
            'keywords' => $product->keywords,
            'visible' => (int) $product->visible,
            'hide_from_search' => (int) $product->hide_from_search,
            'deleted_at' => $product->deleted_at,
            'inventory' => $product->inventory,
            'track_inventory' => $product->track_inventory,
            'taxable' => (int) $product->taxable,
            'type_id' => $product->type_id,
            'is_bundle' => (int) $product->is_bundle,
            'sku' => 'RK456',
            'id' => $product->id,
        ]);
    }

    #[Test]
    public function it_can_updates_by_sku_when_csv_columns_match_attributes(): void
    {
        $product = Product::factory()->create(['sku' => 'RK456', 'unit_price' => 350]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
            ])
        ]));

        $product->refresh();

        $this->assertEquals(85000, $product->unit_price);
        $this->assertDatabaseHas(Product::class, [
            'title' => 'New Chicken Steak',
            'slug' => $product->slug,
            'unit_price' => $product->unit_price,
            'unit_of_issue' => $product->unit_of_issue,
            'weight' => 5.000,
            'keywords' => $product->keywords,
            'visible' => (int) $product->visible,
            'hide_from_search' => (int) $product->hide_from_search,
            'deleted_at' => $product->deleted_at,
            'inventory' => $product->inventory,
            'track_inventory' => $product->track_inventory,
            'taxable' => (int) $product->taxable,
            'type_id' => $product->type_id,
            'is_bundle' => (int) $product->is_bundle,
            'sku' => 'RK456',
            'id' => $product->id,
        ]);
    }

    #[Test]
    public function it_does_not_update_attributes_not_included_in_the_file_during_update_by_sku(): void
    {
        $product = Product::factory()->create([
            'sku' => 'RK456',
            'unit_price' => 350,
            'oos_threshold_inventory' => 10,
            'sale_unit_price' => 200,
            'item_cost' => 320,
            'wholesale_unit_price' => 300,
        ]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product->sku,
                'bar' => 'zoo',
                'subscription_reserve' => 12,
            ])
        ]));

        $product->refresh();

        $this->assertEquals(35000, $product->unit_price);
        $this->assertEquals(20000, $product->sale_unit_price);
        $this->assertEquals(32000, $product->item_cost);
        $this->assertEquals(30000, $product->wholesale_unit_price);


        $this->assertDatabaseHas(Product::class, [
            'title' => $product->title,
            'slug' => $product->slug,
            'unit_price' => $product->unit_price,
            'item_cost' => $product->item_cost,
            'sale_unit_price' => $product->sale_unit_price,
            'unit_of_issue' => $product->unit_of_issue,
            'wholesale_unit_price' => $product->wholesale_unit_price,
            'oos_threshold_inventory' => 12,
            'weight' => number_format($product->weight, 3),
            'keywords' => $product->keywords,
            'visible' => (int) $product->visible,
            'hide_from_search' => (int) $product->hide_from_search,
            'deleted_at' => $product->deleted_at,
            'inventory' => $product->inventory,
            'track_inventory' => $product->track_inventory,
            'taxable' => (int) $product->taxable,
            'type_id' => $product->type_id,
            'is_bundle' => (int) $product->is_bundle,
            'sku' => 'RK456',
            'id' => $product->id,
        ]);
    }

    #[Test]
    public function it_correctly_resolves_type_id(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'type' => 'preorder',
            ])
        ]));

        $product1 = Product::where('title', 'New Beef Steak')->first();

        $this->assertEquals(ProductType::PREORDER->value, $product1->type_id);

        $product2 = Product::factory()->create(['sku' => 'RK456', 'type_id' => ProductType::GIFT_CARD->value]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product2->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'type' => 'preorder',
            ])
        ]));

        $this->assertEquals(ProductType::PREORDER->value, $product2->fresh()->type_id);

        $product3 = Product::factory()->create(['sku' => 'RK426', 'type_id' => ProductType::PREORDER->value]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product3->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
            ])
        ]));

        $this->assertEquals(ProductType::PREORDER->value, $product3->fresh()->type_id);

        $product4 = Product::factory()->create(['sku' => 'RK424', 'type_id' => ProductType::PREORDER->value]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product4->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'type' => 'random word',
            ])
        ]));

        $this->assertEquals(ProductType::PREORDER->value, $product4->fresh()->type_id);
    }

    #[Test]
    public function it_correctly_resolves_tax_status(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'tax_status' => 'on',
            ])
        ]));

        $product1 = Product::where('title', 'New Beef Steak')->first();

        $this->assertEquals(1, $product1->taxable);

        $product2 = Product::factory()->create(['sku' => 'RK456', 'taxable' => 0]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product2->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'tax_status' => 'on',
            ])
        ]));

        $this->assertEquals(1, $product2->fresh()->taxable);

        $product3 = Product::factory()->create(['sku' => 'RK426', 'taxable' => 1]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product3->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
            ])
        ]));

        $this->assertEquals(1, $product3->fresh()->taxable);

        $product4 = Product::factory()->create(['sku' => 'RK424', 'taxable' => 0]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product4->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'tax_status' => 'random word',
            ])
        ]));

        $this->assertEquals(0, $product4->fresh()->taxable);
    }

    #[Test]
    public function it_correctly_resolves_visibility_status(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'visibility_status' => 'visible',
            ])
        ]));

        $product1 = Product::where('title', 'New Beef Steak')->first();

        $this->assertEquals(1, $product1->visible);

        $product2 = Product::factory()->create(['sku' => 'RK456', 'visible' => 1]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product2->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'visibility_status' => 'hidden',
            ])
        ]));

        $this->assertEquals(0, $product2->fresh()->visible);

        $product3 = Product::factory()->create(['sku' => 'RK426', 'visible' => 1]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product3->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
            ])
        ]));

        $this->assertEquals(1, $product3->fresh()->visible);

        $product4 = Product::factory()->create(['sku' => 'RK424', 'visible' => 0]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product4->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'tax_status' => 'random word',
            ])
        ]));

        $this->assertEquals(0, $product4->fresh()->visible);
    }

    #[Test]
    public function it_correctly_resolves_pricing_unit(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'pricing_unit' => 'package',
            ])
        ]));

        $product1 = Product::where('title', 'New Beef Steak')->first();

        $this->assertEquals('package', $product1->unit_of_issue);

        $product2 = Product::factory()->create(['sku' => 'RK456', 'unit_of_issue' => 'package']);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product2->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'pricing_unit' => 'weight',
            ])
        ]));

        $this->assertEquals('weight', $product2->fresh()->unit_of_issue);

        $product3 = Product::factory()->create(['sku' => 'RK426', 'unit_of_issue' => 'weight']);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product3->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
            ])
        ]));

        $this->assertEquals('weight', $product3->fresh()->unit_of_issue);

        $product4 = Product::factory()->create(['sku' => 'RK424', 'unit_of_issue' => 'weight']);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product4->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'pricing_unit' => 'random word',
            ])
        ]));

        $this->assertEquals('weight', $product4->fresh()->unit_of_issue);
    }

    #[Test]
    public function it_correctly_resolves_packing_group(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'packing_group' => 'dry',
            ])
        ]));

        $product1 = Product::where('title', 'New Beef Steak')->first();

        $this->assertEquals(PackingGroup::dry(), $product1->packingGroup->id);

        $product2 = Product::factory()->create(['sku' => 'RK456', 'inventory_type' => PackingGroup::frozen()]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product2->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'packing_group' => 'fresh',
            ])
        ]));

        $this->assertEquals(PackingGroup::isFresh(), $product2->fresh()->packingGroup->id);

        $product3 = Product::factory()->create(['sku' => 'RK426', 'inventory_type' => PackingGroup::seasonal()]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product3->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
            ])
        ]));

        $this->assertEquals(PackingGroup::seasonal(), $product3->fresh()->packingGroup->id);

        $product4 = Product::factory()->create(['sku' => 'RK424', 'inventory_type' => PackingGroup::digital()]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product4->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'pricing_unit' => 'random word',
            ])
        ]));

        $this->assertEquals(PackingGroup::digital(), $product4->fresh()->packingGroup->id);
    }

    #[Test]
    public function it_correctly_resolves_vendor(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'vendor' => 'A Good Vendor',
            ])
        ]));

        $product1 = Product::where('title', 'New Beef Steak')->first();

        $this->assertEquals('A Good Vendor', $product1->vendor->title);

        $product2 = Product::factory()->create(['sku' => 'RK456', 'vendor_id' => Vendor::factory()->create(['title' => 'Vendor Unnamed'])]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product2->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'vendor' => 'A Big Vendor',
            ])
        ]));

        $this->assertEquals('A Big Vendor', $product2->fresh()->vendor->title);

        $product3 = Product::factory()->create(['sku' => 'RK426', 'vendor_id' => Vendor::factory()->create(['title' => 'Vendor Unnamed'])]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product3->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
            ])
        ]));

        $this->assertEquals('Vendor Unnamed', $product3->fresh()->vendor->title);

        $product4 = Product::factory()->create(['sku' => 'RK424', 'vendor_id' => Vendor::factory()->create(['title' => 'Vendor Unnamed 2'])]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product4->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'vendor' => null,
            ])
        ]));

        $this->assertEquals('Vendor Unnamed 2', $product4->fresh()->vendor->title);
    }

    #[Test]
    public function it_adjusts_the_inventory_accurately(): void
    {
        Event::fake();
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'on_site_inventory_actual' => 50,
                'on_site_inventory_to_add' => 19,
                'on_site_inventory_to_remove' => 4,
            ])
        ]));

        Event::assertNotDispatched(InventoryAdjusted::class);

        $product1 = Product::where('title', 'New Beef Steak')->first();

        $this->assertEquals(50, $product1->fresh()->inventory);

        $product2 = Product::factory()->create(['sku' => 'RK456', 'inventory' => 50]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product2->sku,
                'title' => 'New Chicken Steak',
                'unit_price' => 850,
                'weight_lb' => 5,
                'on_site_inventory_actual' => 50,
                'on_site_inventory_to_add' => 19,
                'on_site_inventory_to_remove' => 4,
            ])
        ]));

        Event::assertDispatched(InventoryAdjusted::class, fn(InventoryAdjusted $event) => $event->newQty === 65
            && $event->originalQty === 50
            && $event->productId === $product2->id);

        $this->assertEquals(65, $product2->fresh()->inventory);

    }

    #[Test]
    public function it_fires_the_inventory_adjusted_event_when_setting_actual_inventory_values(): void
    {
        Event::fake();

        $user = User::factory()->create();

        $product = Product::factory()->create(['sku' => 'RK456', 'inventory' => 50]);

        $import = new ProductImport('update_by_sku');
        $import->collection(collect([
            collect([
                'sku' => $product->sku,
                'on_site_inventory_actual' => 150,
            ])
        ]));

        Event::assertDispatched(InventoryAdjusted::class, fn(InventoryAdjusted $event) =>
            $event->newQty === 150
            && $event->originalQty === 50
            && $event->productId === $product->id
        );
    }

    #[Test]
    public function it_correctly_resolves_a_single_protocol(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'pricing_unit' => 'package',
                'protocol' => '100% Glutten Free',
            ])
        ]));

        $this->assertDatabaseHas(Product::class, [
            'title' => 'New Beef Steak',
            'vendor_id' => null,
        ]);

        $this->assertDatabaseHas(Protocol::class, [
            'title' => '100% Glutten Free',
            'description' => null,
        ]);

        $product = Product::where('title', 'New Beef Steak')->first();
        $protocol = Protocol::where('title', '100% Glutten Free')->first();

        $this->assertDatabaseHas('product_protocol', [
            'product_id' => $product->id,
            'protocol_id' => $protocol->id,
        ]);
    }

    #[Test]
    public function it_correctly_resolves_multiple_protocols(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'pricing_unit' => 'package',
                'protocol' => '100% Glutten Free, New thing',
            ])
        ]));

        $this->assertDatabaseHas(Product::class, [
            'title' => 'New Beef Steak',
            'vendor_id' => null,
        ]);

        $this->assertDatabaseHas(Protocol::class, [
            'title' => '100% Glutten Free',
            'description' => null,
        ]);

        $this->assertDatabaseHas(Protocol::class, [
            'title' => 'New thing',
            'description' => null,
        ]);

        $product = Product::where('title', 'New Beef Steak')->first();
        $protocol1 = Protocol::where('title', '100% Glutten Free')->first();
        $protocol2 = Protocol::where('title', 'New thing')->first();

        $this->assertDatabaseHas('product_protocol', [
            'product_id' => $product->id,
            'protocol_id' => $protocol1->id,
        ]);

        $this->assertDatabaseHas('product_protocol', [
            'product_id' => $product->id,
            'protocol_id' => $protocol2->id,
        ]);
    }

    #[Test]
    public function it_correctly_resolves_a_single_collection(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'pricing_unit' => 'package',
                'collection' => 'Grass-Fed',
            ])
        ]));

        $this->assertDatabaseHas(Product::class, [
            'title' => 'New Beef Steak',
            'vendor_id' => null,
        ]);

        $this->assertDatabaseHas(Collection::class, [
            'title' => 'Grass-Fed',
            'description' => '',
        ]);

        $product = Product::where('title', 'New Beef Steak')->first();
        $collection = Collection::where('title', 'Grass-Fed')->first();

        $this->assertDatabaseHas('collection_product', [
            'product_id' => $product->id,
            'collection_id' => $collection->id,
        ]);
    }

    #[Test]
    public function it_correctly_resolves_multiple_collections(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'pricing_unit' => 'package',
                'collection' => 'Grass-Fed, Wholesale Products',
            ])
        ]));

        $this->assertDatabaseHas(Product::class, [
            'title' => 'New Beef Steak',
            'vendor_id' => null,
        ]);

        $this->assertDatabaseHas(Collection::class, [
            'title' => 'Grass-Fed',
            'description' => '',
        ]);

        $this->assertDatabaseHas(Collection::class, [
            'title' => 'Wholesale Products',
            'description' => '',
        ]);

        $product = Product::where('title', 'New Beef Steak')->first();
        $collection1 = Collection::where('title', 'Grass-Fed')->first();
        $collection2 = Collection::where('title', 'Wholesale Products')->first();

        $this->assertDatabaseHas('collection_product', [
            'product_id' => $product->id,
            'collection_id' => $collection1->id,
        ]);

        $this->assertDatabaseHas('collection_product', [
            'product_id' => $product->id,
            'collection_id' => $collection2->id,
        ]);
    }

    #[Test]
    public function it_correctly_resolves_a_single_tag(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'pricing_unit' => 'package',
                'tag' => 'Grass-Fed Tag',
            ])
        ]));

        $this->assertDatabaseHas(Product::class, [
            'title' => 'New Beef Steak',
            'vendor_id' => null,
        ]);

        $this->assertDatabaseHas(Tag::class, [
            'title' => 'Grass-Fed Tag',
        ]);

        $product = Product::where('title', 'New Beef Steak')->first();
        $tag = Tag::where('title', 'Grass-Fed Tag')->first();

        $this->assertDatabaseHas('product_tag', [
            'product_id' => $product->id,
            'tag_id' => $tag->id,
        ]);
    }

    #[Test]
    public function it_correctly_resolves_multiple_tags(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'New Beef Steak',
                'unit_price' => 350,
                'weight_lb' => 15,
                'pricing_unit' => 'package',
                'tag' => 'Grass-Fed Tag, Sale Only',
            ])
        ]));

        $this->assertDatabaseHas(Product::class, [
            'title' => 'New Beef Steak',
            'vendor_id' => null,
        ]);

        $this->assertDatabaseHas(Tag::class, [
            'title' => 'Grass-Fed Tag',
        ]);

        $this->assertDatabaseHas(Tag::class, [
            'title' => 'Sale Only',
        ]);

        $product = Product::where('title', 'New Beef Steak')->first();
        $tag1 = Tag::where('title', 'Grass-Fed Tag')->first();
        $tag2 = Tag::where('title', 'Sale Only')->first();

        $this->assertDatabaseHas('product_tag', [
            'product_id' => $product->id,
            'tag_id' => $tag1->id,
        ]);

        $this->assertDatabaseHas('product_tag', [
            'product_id' => $product->id,
            'tag_id' => $tag2->id,
        ]);
    }

    #[Test]
    public function it_creates_price_tier_when_quantity_is_specified(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'Quantity Product',
                'unit_price' => 200,
                'sale_unit_price' => 190,
                'quantity' => 1,
            ])
        ]));

        $product = Product::where('title', 'Quantity Product')->first();

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => 20000,
            'sale_unit_price' => 19000,
        ]);
    }

    #[Test]
    public function it_creates_multiple_price_tiers_for_same_product(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'title' => 'Tiered Product',
                'unit_price' => 200,
                'sale_unit_price' => 190,
                'quantity' => 1,
            ])
        ]));

        $product = Product::where('title', 'Tiered Product')->first();

        $import->collection(collect([
            collect([
                'title' => 'Tiered Product',
                'unit_price' => 150,
                'sale_unit_price' => 140,
                'quantity' => 10,
            ])
        ]));

        $import->collection(collect([
            collect([
                'title' => 'Tiered Product',
                'unit_price' => 100,
                'sale_unit_price' => 90,
                'quantity' => 50,
            ])
        ]));

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => 20000,
            'sale_unit_price' => 19000,
        ]);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 10,
            'unit_price' => 15000,
            'sale_unit_price' => 14000,
        ]);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 50,
            'unit_price' => 10000,
            'sale_unit_price' => 9000,
        ]);

        $this->assertEquals(1, Product::where('title', 'Tiered Product')->count());
    }

    #[Test]
    public function it_updates_product_price_tiers_using_update_by_id_with_quantity(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'sku' => 'DPTUP',
                'title' => 'Direct Price Tier Update Product',
                'unit_price' => 200,
                'sale_unit_price' => 190,
            ])
        ]));

        $product = Product::where('sku', 'DPTUP')->first();

        $import = new ProductImport('update_by_id', $user);
        $import->collection(collect([
            collect([
                'id' => $product->id,
                'unit_price' => 150,
                'sale_unit_price' => 140,
                'quantity' => 10,
            ])
        ]));

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => 20000,
            'sale_unit_price' => 19000,
        ]);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 10,
            'unit_price' => 15000,
            'sale_unit_price' => 14000,
        ]);

        $import->collection(collect([
            collect([
                'id' => $product->id,
                'unit_price' => 100,
                'sale_unit_price' => 90,
                'quantity' => 50,
            ])
        ]));

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 50,
            'unit_price' => 10000,
            'sale_unit_price' => 9000,
        ]);

        $this->assertEquals(1, Product::where('sku', 'DPTUP')->count());
    }

    #[Test]
    public function it_updates_product_price_tiers_using_update_by_sku_with_quantity(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'sku' => 'DPTUP-SKU',
                'title' => 'SKU Price Tier Update Product',
                'unit_price' => 200,
                'sale_unit_price' => 190,
            ])
        ]));

        $product = Product::where('sku', 'DPTUP-SKU')->first();

        $import = new ProductImport('update_by_sku', $user);
        $import->collection(collect([
            collect([
                'sku' => $product->sku,
                'unit_price' => 150,
                'sale_unit_price' => 140,
                'quantity' => 10,
            ])
        ]));

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => 20000,
            'sale_unit_price' => 19000,
        ]);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 10,
            'unit_price' => 15000,
            'sale_unit_price' => 14000,
        ]);

        $this->assertEquals(1, Product::where('sku', 'DPTUP-SKU')->count());
    }

    #[Test]
    public function it_can_update_both_price_tiers_and_product_attributes_simultaneously(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'sku' => 'COMBO-UPDATE',
                'title' => 'Combo Update Product',
                'unit_price' => 200,
                'sale_unit_price' => 190,
                'summary' => 'Original summary',
            ])
        ]));

        $product = Product::where('sku', 'COMBO-UPDATE')->first();

        // Update both price tier and product attributes
        $import = new ProductImport('update_by_id', $user);
        $import->collection(collect([
            collect([
                'id' => $product->id,
                'title' => 'Updated Combo Product',
                'summary' => 'Updated summary',
                'unit_price' => 150,
                'sale_unit_price' => 140,
                'quantity' => 10,
            ])
        ]));

        $product->refresh();

        // Check that product attributes were updated
        $this->assertEquals('Updated Combo Product', $product->title);
        $this->assertEquals('Updated summary', $product->summary);

        // Check that price tiers were created/updated
        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => 20000, // Original price from product creation
            'sale_unit_price' => 19000,
        ]);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 10,
            'unit_price' => 15000, // New price tier
            'sale_unit_price' => 14000,
        ]);
    }

    #[Test]
    public function it_can_update_multiple_price_tiers_for_same_product(): void
    {
        $user = User::factory()->create();

        $import = new ProductImport('create', $user);
        $import->collection(collect([
            collect([
                'sku' => 'MULTI-TIER',
                'title' => 'Multi Tier Product',
                'unit_price' => 200,
                'sale_unit_price' => 190,
            ])
        ]));

        $product = Product::where('sku', 'MULTI-TIER')->first();

        // Update multiple price tiers
        $import = new ProductImport('update_by_sku', $user);
        $import->collection(collect([
            collect([
                'sku' => $product->sku,
                'unit_price' => 150,
                'sale_unit_price' => 140,
                'quantity' => 10,
            ]),
            collect([
                'sku' => $product->sku,
                'unit_price' => 100,
                'sale_unit_price' => 90,
                'quantity' => 50,
            ]),
            collect([
                'sku' => $product->sku,
                'unit_price' => 75,
                'sale_unit_price' => 65,
                'quantity' => 100,
            ])
        ]));

        // Check all price tiers were created
        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => 20000, // Original from product creation
            'sale_unit_price' => 19000,
        ]);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 10,
            'unit_price' => 15000,
            'sale_unit_price' => 14000,
        ]);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 50,
            'unit_price' => 10000,
            'sale_unit_price' => 9000,
        ]);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 100,
            'unit_price' => 7500,
            'sale_unit_price' => 6500,
        ]);

        $this->assertEquals(1, Product::where('sku', 'MULTI-TIER')->count());
    }
}
